import React from 'react';
import { SupportedLanguage, LanguageOption } from '../types';

interface LanguageSelectorProps {
  options: LanguageOption[];
  selectedLanguage: SupportedLanguage;
  onLanguageChange: (languageCode: SupportedLanguage) => void;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({ options, selectedLanguage, onLanguageChange }) => {
  return (
    <div className="flex flex-col items-end w-full">
      <label htmlFor="language-select" className="block text-xs font-medium text-slate-400 mb-1 mr-1">
        Idioma para Traduções:
      </label>
      <select
        id="language-select"
        value={selectedLanguage}
        onChange={(e) => onLanguageChange(e.target.value as SupportedLanguage)}
        className="w-full sm:w-auto bg-slate-700 border border-slate-600 text-slate-200 text-sm rounded-lg focus:ring-sky-500 focus:border-sky-500 block p-2 shadow-md appearance-none"
        aria-label="Selecionar idioma para traduções"
      >
        {options.map(option => (
          <option key={option.code} value={option.code}>
            {option.name}
          </option>
        ))}
      </select>
    </div>
  );
};

export default LanguageSelector;
