import { GoogleGenAI, GenerateContentResponse } from "@google/genai";
import { AIInsights, SentenceExample, AITip, PronunciationEvaluation, SupportedLanguage, languageNameMap } from '../types';

const API_KEY = process.env.API_KEY;
if (!API_KEY) {
  console.error("API_KEY environment variable not set. Gemini API calls will fail.");
}
const ai = new GoogleGenAI({ apiKey: API_KEY || "MISSING_API_KEY" }); 

const TEXT_MODEL_NAME = 'gemini-2.5-flash-preview-04-17';
const MAX_RETRIES = 3;
const RETRY_DELAY_MS = 1000;

async function generateWithRetry<T, P>(
  generateFn: (params: P) => Promise<T>,
  params: P,
  attempt: number = 1
): Promise<T> {
  try {
    return await generateFn(params);
  } catch (error) {
    console.error(`Gemini API call attempt ${attempt} failed:`, error);
    if (attempt >= MAX_RETRIES) {
      throw error; 
    }
    await new Promise(resolve => setTimeout(resolve, RETRY_DELAY_MS * attempt));
    return generateWithRetry(generateFn, params, attempt + 1);
  }
}

export const fetchInsightsForWord = async (word: string, targetLanguage: SupportedLanguage): Promise<AIInsights | null> => {
  if (!API_KEY) {
     console.error("Cannot fetch insights: API_KEY is missing.");
     return null;
  }
  const targetLangName = languageNameMap[targetLanguage] || targetLanguage;

  const prompt = `Você é um assistente de aprendizado de inglês. Para a palavra "${word}", forneça:
1.  Exatamente 2 frases curtas de exemplo em inglês americano cotidiano.
2.  Para cada frase de exemplo, forneça uma tradução para o idioma ${targetLangName}.
3.  Para cada frase de exemplo em inglês, forneça uma forma 'shortened' (encurtada/coloquial) que um falante nativo americano usaria naturalmente em uma conversa casual. Isso pode incluir contrações comuns (ex: 'going to' -> 'gonna', 'want to' -> 'wanna'), palavras omitidas que são entendidas pelo contexto, ou gírias leves, se apropriado e comum. Se a frase original já for concisa e natural para uma conversa casual, ou se não houver um encurtamento comum e significativamente diferente, você pode repetir a frase original ou uma variação mínima no campo 'shortened'. O objetivo é mostrar como a frase pode soar mais fluida e menos formal no dia a dia.
4.  Exatamente 1 dica prática de uso para esta palavra em inglês americano cotidiano, e também forneça a tradução desta dica para o idioma ${targetLangName}.

Formate a resposta EXATAMENTE como JSON abaixo, sem nenhum texto adicional antes ou depois do JSON, e sem markdown:
{
  "sentences": [
    { "original": "Exemplo de frase 1.", "translation": "Tradução da frase 1 para ${targetLangName}.", "shortened": "Forma encurtada da frase 1." },
    { "original": "Exemplo de frase 2.", "translation": "Tradução da frase 2 para ${targetLangName}.", "shortened": "Forma encurtada da frase 2." }
  ],
  "tips": [{ "original": "Dica de uso concisa em inglês.", "translation": "Tradução concisa da dica para ${targetLangName}." }]
}`;

  try {
    const response: GenerateContentResponse = await generateWithRetry(
        (params) => ai.models.generateContent(params), 
        {
            model: TEXT_MODEL_NAME,
            contents: prompt,
            config: { responseMimeType: "application/json" }
        }
    );

    let jsonStr = response.text.trim();
    const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
    const match = jsonStr.match(fenceRegex);
    if (match && match[2]) {
      jsonStr = match[2].trim();
    }
    
    const parsed = JSON.parse(jsonStr);

    if (parsed && Array.isArray(parsed.sentences) && Array.isArray(parsed.tips)) {
        const validatedSentences: SentenceExample[] = parsed.sentences.filter(
            (s: any) => typeof s === 'object' && s !== null &&
                         typeof s.original === 'string' &&
                         typeof s.translation === 'string' &&
                         typeof s.shortened === 'string'
        ).slice(0, 2); 

        const validatedTips: AITip[] = parsed.tips.filter(
            (t: any) => typeof t === 'object' && t !== null &&
                         typeof t.original === 'string' &&
                         typeof t.translation === 'string'
        ).slice(0, 1); 

        return {
            sentences: validatedSentences,
            tips: validatedTips
        };
    }
    console.error("Parsed AI response is not in the expected format:", parsed);
    return null;

  } catch (error) {
    console.error("Error fetching insights from Gemini API:", error);
    return null;
  }
};

export const transcribeAudioPronunciation = async (audioBlob: Blob): Promise<string | null> => {
  if (!API_KEY) {
    console.error("Cannot transcribe audio: API_KEY is missing.");
    return null;
  }
  try {
    const base64Audio = await new Promise<string>((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve((reader.result as string).split(',')[1]);
      reader.onerror = reject;
      reader.readAsDataURL(audioBlob);
    });

    const audioPart = {
      inlineData: {
        mimeType: audioBlob.type || 'audio/webm', // More robust MIME type handling
        data: base64Audio,
      },
    };
    
    // Refined prompt for cleaner transcription
    const textPart = { text: "Transcribe the spoken English in this audio. Return only the transcribed text. Do not include any additional commentary, analysis, or punctuation unless it is clearly part of the speech (e.g., a question mark if spoken as a question)." };

    const response: GenerateContentResponse = await generateWithRetry(
        (params) => ai.models.generateContent(params),
        {
            model: TEXT_MODEL_NAME, 
            contents: { parts: [audioPart, textPart] },
        }
    );
    
    if (response && typeof response.text === 'string') {
      // Minimal cleaning, assuming the refined prompt helps.
      // Important: preserve internal spaces, just trim ends.
      return response.text.trim();
    } else {
      console.error("Error transcribing audio with Gemini API: response.text is undefined or not a string.", response);
      return null;
    }
  } catch (error) {
    console.error("Error transcribing audio with Gemini API:", error);
    return null;
  }
};


export const evaluatePronunciationViaAI = async (
  expectedText: string,
  transcribedText: string,
  targetLanguageBeingLearned: string = "American English" // e.g. "American English"
): Promise<PronunciationEvaluation | null> => {
  if (!API_KEY) {
    console.error("Cannot evaluate pronunciation: API_KEY is missing.");
    return null;
  }

  const prompt = `Você é um avaliador de pronúncia de ${targetLanguageBeingLearned}.
O usuário está tentando dizer: "${expectedText}"
A transcrição da pronúncia do usuário foi: "${transcribedText}"

Por favor, avalie a pronúncia do usuário. Considere a clareza, correção e naturalidade em comparação com um falante nativo de ${targetLanguageBeingLearned}.

Forneça sua resposta EXATAMENTE no formato JSON abaixo, sem texto adicional antes ou depois:
{
  "evaluation": "Sua avaliação geral aqui (ex: 'Excelente!', 'Bom, mas com alguns pontos a melhorar', 'Precisa de mais prática').",
  "advice": "Conselhos específicos para melhorar (ex: 'A palavra X foi pronunciada como Y, tente focar no som Z.' ou 'A entonação soou um pouco hesitante, tente ser mais confiante.'). Se a pronúncia foi perfeita, diga algo como 'Pronúncia clara e precisa!'.",
  "similarityAssessment": "Uma breve avaliação da similaridade (ex: 'Muito similar ao esperado', 'Com algumas diferenças notáveis', 'Bastante diferente do esperado')."
}`;

  try {
    const response: GenerateContentResponse = await generateWithRetry(
      (params) => ai.models.generateContent(params),
      {
        model: TEXT_MODEL_NAME,
        contents: prompt,
        config: { responseMimeType: "application/json" }
      }
    );

    let jsonStr = response.text.trim();
    const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
    const match = jsonStr.match(fenceRegex);
    if (match && match[2]) {
      jsonStr = match[2].trim();
    }

    const parsed = JSON.parse(jsonStr) as PronunciationEvaluation;

    if (parsed && typeof parsed.evaluation === 'string' && typeof parsed.advice === 'string' && typeof parsed.similarityAssessment === 'string') {
      return parsed;
    }
    console.error("Parsed AI pronunciation evaluation is not in the expected format:", parsed);
    return null;

  } catch (error) {
    console.error("Error evaluating pronunciation with Gemini API:", error);
    return null;
  }
};
