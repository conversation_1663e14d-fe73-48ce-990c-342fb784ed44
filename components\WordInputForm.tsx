
import React, { useState } from 'react';
import { PlusIcon } from './icons';

interface WordInputFormProps {
  onAddWord: (wordText: string) => void;
  isLoading: boolean;
}

const WordInputForm: React.FC<WordInputFormProps> = ({ onAddWord, isLoading }) => {
  const [newWord, setNewWord] = useState<string>('');

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (newWord.trim() && !isLoading) {
      onAddWord(newWord.trim());
      setNewWord('');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="mb-8 p-6 bg-slate-800 rounded-xl shadow-2xl flex items-center gap-4">
      <input
        type="text"
        value={newWord}
        onChange={(e) => setNewWord(e.target.value)}
        placeholder="Digite uma palavra em inglês..."
        className="flex-grow p-3 bg-slate-700 text-slate-100 rounded-lg border border-slate-600 focus:ring-2 focus:ring-sky-500 focus:border-sky-500 outline-none transition-all duration-300"
        disabled={isLoading}
      />
      <button
        type="submit"
        className="bg-sky-500 hover:bg-sky-600 text-white font-semibold py-3 px-6 rounded-lg flex items-center gap-2 transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
        disabled={isLoading || !newWord.trim()}
      >
        <PlusIcon className="w-5 h-5" />
        Adicionar
      </button>
    </form>
  );
};

export default WordInputForm;
