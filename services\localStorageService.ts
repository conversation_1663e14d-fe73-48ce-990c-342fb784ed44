import { WordEntry } from '../types';

export interface LocalWordEntry extends WordEntry {
  localId?: string; // ID temporário para palavras não sincronizadas
  syncStatus: 'synced' | 'pending' | 'conflict' | 'local-only';
  lastModified: string;
  needsSync?: boolean;
}

export interface LocalStorageData {
  words: LocalWordEntry[];
  lastSyncTimestamp: string | null;
  userId: string | null;
}

const STORAGE_KEY = 'lingolearn_data';
const BACKUP_KEY = 'lingolearn_backup';

// Função para gerar ID temporário
const generateTempId = (): string => {
  return `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Salvar dados no localStorage
export const saveToLocalStorage = (data: LocalStorageData): void => {
  try {
    // Fazer backup dos dados atuais antes de salvar novos
    const currentData = localStorage.getItem(STORAGE_KEY);
    if (currentData) {
      localStorage.setItem(BACKUP_KEY, currentData);
    }

    localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
  } catch (error) {
    console.error('Erro ao salvar no localStorage:', error);
  }
};

// Carregar dados do localStorage
export const loadFromLocalStorage = (): LocalStorageData => {
  try {
    const data = localStorage.getItem(STORAGE_KEY);
    if (data) {
      return JSON.parse(data);
    }
  } catch (error) {
    console.error('Erro ao carregar do localStorage:', error);
    // Tentar carregar backup
    try {
      const backupData = localStorage.getItem(BACKUP_KEY);
      if (backupData) {
        console.log('Carregando dados do backup...');
        return JSON.parse(backupData);
      }
    } catch (backupError) {
      console.error('Erro ao carregar backup:', backupError);
    }
  }

  return {
    words: [],
    lastSyncTimestamp: null,
    userId: null
  };
};

// Adicionar palavra localmente
export const addWordLocally = (text: string, userId: string): LocalWordEntry => {
  const data = loadFromLocalStorage();

  const newWord: LocalWordEntry = {
    id: generateTempId(),
    localId: generateTempId(),
    text: text.toLowerCase(),
    added_date: new Date().toISOString(),
    user_id: userId,
    syncStatus: 'pending',
    lastModified: new Date().toISOString(),
    needsSync: true,
    ai_insights: null,
    practiced_text: null,
    transcription: null,
    pronunciation_evaluation: null
  };

  data.words.unshift(newWord);
  data.userId = userId;
  saveToLocalStorage(data);

  return newWord;
};

// Remover palavra localmente
export const removeWordLocally = (id: string): boolean => {
  const data = loadFromLocalStorage();
  const wordIndex = data.words.findIndex(word => word.id === id || word.localId === id);

  if (wordIndex === -1) return false;

  const word = data.words[wordIndex];

  // Se a palavra já foi sincronizada, marcar para deleção
  if (word.syncStatus === 'synced' && !word.localId?.startsWith('temp_')) {
    word.syncStatus = 'pending';
    word.needsSync = true;
    word.lastModified = new Date().toISOString();
    // Adicionar flag de deleção
    (word as any).markedForDeletion = true;
  } else {
    // Se é uma palavra local, remover completamente
    data.words.splice(wordIndex, 1);
  }

  saveToLocalStorage(data);
  return true;
};

// Atualizar palavra localmente
export const updateWordLocally = (id: string, updates: Partial<WordEntry>): boolean => {
  const data = loadFromLocalStorage();
  const wordIndex = data.words.findIndex(word => word.id === id || word.localId === id);

  if (wordIndex === -1) return false;

  const word = data.words[wordIndex];
  Object.assign(word, updates);
  word.syncStatus = word.syncStatus === 'synced' ? 'pending' : word.syncStatus;
  word.lastModified = new Date().toISOString();
  word.needsSync = true;

  saveToLocalStorage(data);
  return true;
};

// Obter todas as palavras locais
export const getAllWordsLocally = (userId?: string): LocalWordEntry[] => {
  const data = loadFromLocalStorage();

  if (userId && data.userId !== userId) {
    // Se o usuário mudou, limpar dados locais
    return [];
  }

  // Filtrar palavras marcadas para deleção
  return data.words.filter(word => !(word as any).markedForDeletion);
};

// Obter palavras que precisam ser sincronizadas
export const getWordsNeedingSync = (): LocalWordEntry[] => {
  const data = loadFromLocalStorage();
  return data.words.filter(word => word.needsSync || word.syncStatus === 'pending');
};

// Marcar palavra como sincronizada
export const markWordAsSynced = (localId: string, supabaseId: string): void => {
  const data = loadFromLocalStorage();
  const word = data.words.find(w => w.localId === localId || w.id === localId);

  if (word) {
    word.id = supabaseId;
    word.syncStatus = 'synced';
    word.needsSync = false;
    word.lastModified = new Date().toISOString();
    saveToLocalStorage(data);
  }
};

// Atualizar timestamp da última sincronização
export const updateLastSyncTimestamp = (): void => {
  const data = loadFromLocalStorage();
  data.lastSyncTimestamp = new Date().toISOString();
  saveToLocalStorage(data);
};

// Limpar dados locais (logout)
export const clearLocalData = (): void => {
  try {
    localStorage.removeItem(STORAGE_KEY);
    localStorage.removeItem(BACKUP_KEY);
  } catch (error) {
    console.error('Erro ao limpar dados locais:', error);
  }
};

// Verificar se há dados pendentes de sincronização
export const hasPendingSync = (): boolean => {
  const data = loadFromLocalStorage();
  return data.words.some(word => word.needsSync || word.syncStatus === 'pending');
};

// Obter estatísticas de sincronização
export const getSyncStats = () => {
  const data = loadFromLocalStorage();
  const stats = {
    total: data.words.length,
    synced: 0,
    pending: 0,
    localOnly: 0,
    conflicts: 0
  };

  data.words.forEach(word => {
    switch (word.syncStatus) {
      case 'synced':
        stats.synced++;
        break;
      case 'pending':
        stats.pending++;
        break;
      case 'local-only':
        stats.localOnly++;
        break;
      case 'conflict':
        stats.conflicts++;
        break;
    }
  });

  return stats;
};