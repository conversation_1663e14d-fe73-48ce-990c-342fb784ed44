import React, { useState, useRef, useEffect } from 'react';
import { WordEntry, SentenceExample, SupportedLanguage, PronunciationEvaluation } from '../types';
import { fetchInsightsForWord, transcribeAudioPronunciation, evaluatePronunciationViaAI } from '../services/geminiService';
import LoadingSpinner from './LoadingSpinner';
import { TrashIcon, SparklesIcon, MicrophoneIcon, StopIcon, CheckCircleIcon, XCircleIcon } from './icons';

interface WordItemProps {
  wordEntry: WordEntry;
  onRemoveWord: (id: string) => void;
  onUpdateWord: (updatedWord: WordEntry) => void;
  selectedLanguage: SupportedLanguage;
}

type PracticeTargetType = 'word' | 'sentence1' | 'sentence2';

const WordItem: React.FC<WordItemProps> = ({ wordEntry, onRemoveWord, onUpdateWord, selectedLanguage }) => {
  const [isLoadingInsights, setIsLoadingInsights] = useState(false);
  const [isLoadingTranscription, setIsLoadingTranscription] = useState(false);
  const [isLoadingEvaluation, setIsLoadingEvaluation] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [practiceTarget, setPracticeTarget] = useState<string>(wordEntry.text);
  const [practiceTargetType, setPracticeTargetType] = useState<PracticeTargetType>('word');

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  useEffect(() => {
    let currentTargetText = wordEntry.text;
    if (practiceTargetType === 'sentence1' && wordEntry.ai_insights?.sentences?.[0]) {
      currentTargetText = wordEntry.ai_insights.sentences[0].original;
    } else if (practiceTargetType === 'sentence2' && wordEntry.ai_insights?.sentences?.[1]) {
      currentTargetText = wordEntry.ai_insights.sentences[1].original;
    } else if (practiceTargetType !== 'word') {
      setPracticeTargetType('word'); // Fallback if sentence not available
    }
    setPracticeTarget(currentTargetText);
  }, [wordEntry.text, wordEntry.ai_insights, practiceTargetType]);


  const handleFetchInsights = async () => {
    setIsLoadingInsights(true);
    setError(null);
    try {
      const insights = await fetchInsightsForWord(wordEntry.text, selectedLanguage);
      if (insights) {
        onUpdateWord({ ...wordEntry, ai_insights: insights });
      } else {
        setError("Não foi possível buscar dicas da IA. Tente novamente.");
      }
    } catch (e) {
      console.error(e);
      setError("Erro ao buscar dicas da IA.");
    } finally {
      setIsLoadingInsights(false);
    }
  };

  const startRecording = async () => {
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        mediaRecorderRef.current = new MediaRecorder(stream, { mimeType: 'audio/webm;codecs=opus' }); // Specify MIME type for better compatibility
        audioChunksRef.current = [];

        mediaRecorderRef.current.ondataavailable = (event) => {
          audioChunksRef.current.push(event.data);
        };

        mediaRecorderRef.current.onstop = async () => {
          const audioBlob = new Blob(audioChunksRef.current, { type: mediaRecorderRef.current?.mimeType || 'audio/webm' });
          setIsLoadingTranscription(true);
          setError(null);
          let transcriptionText: string | null = null;
          try {
            transcriptionText = await transcribeAudioPronunciation(audioBlob);
            onUpdateWord({ ...wordEntry, practiced_text: practiceTarget, transcription: transcriptionText || "Falha na transcrição", pronunciation_evaluation: null });
          } catch (e) {
             console.error("Transcription error:", e);
             setError("Erro ao transcrever áudio.");
             onUpdateWord({ ...wordEntry, practiced_text: practiceTarget, transcription: "Erro na transcrição", pronunciation_evaluation: null });
          } finally {
            setIsLoadingTranscription(false);
            stream.getTracks().forEach(track => track.stop());
          }

          if (transcriptionText) {
            setIsLoadingEvaluation(true);
            try {
              const evaluationResult = await evaluatePronunciationViaAI(practiceTarget, transcriptionText);
              onUpdateWord({ ...wordEntry, practiced_text: practiceTarget, transcription: transcriptionText, pronunciation_evaluation: evaluationResult });
            } catch (e) {
              console.error("Evaluation error:", e);
              setError("Erro ao avaliar a pronúncia.");
              onUpdateWord({ ...wordEntry, practiced_text: practiceTarget, transcription: transcriptionText, pronunciation_evaluation: null });
            } finally {
              setIsLoadingEvaluation(false);
            }
          } else {
            onUpdateWord({ ...wordEntry, practiced_text: practiceTarget, transcription: "Falha na transcrição", pronunciation_evaluation: null });
          }
        };

        mediaRecorderRef.current.start();
        setIsRecording(true);
        setError(null);
        onUpdateWord({ ...wordEntry, practiced_text: practiceTarget, transcription: undefined, pronunciation_evaluation: undefined });

      } catch (err) {
        console.error("Error accessing microphone:", err);
        setError("Não foi possível acessar o microfone. Verifique as permissões e se o formato de áudio é suportado.");
         if (err instanceof DOMException && err.name === 'NotSupportedError') {
            setError("Formato de gravação de áudio não suportado pelo navegador. Tente usar Opus/WebM se disponível.");
        }
      }
    } else {
      setError("Gravação de áudio não suportada neste navegador.");
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false); 
    }
  };

  const handlePracticeTargetChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTargetType = e.target.value as PracticeTargetType;
    setPracticeTargetType(newTargetType);
    let newText = wordEntry.text;
    if (newTargetType === 'sentence1' && wordEntry.ai_insights?.sentences?.[0]) {
      newText = wordEntry.ai_insights.sentences[0].original;
    } else if (newTargetType === 'sentence2' && wordEntry.ai_insights?.sentences?.[1]) {
      newText = wordEntry.ai_insights.sentences[1].original;
    }
    setPracticeTarget(newText);
    onUpdateWord({...wordEntry, practiced_text: newText, transcription: undefined, pronunciation_evaluation: undefined });
  };
  
  const PronunciationFeedback: React.FC = () => {
    if (!wordEntry.practiced_text && !wordEntry.transcription && !wordEntry.pronunciation_evaluation) return null;

    const evaluation = wordEntry.pronunciation_evaluation;
    let feedbackIcon = null;
    let evaluationColor = "text-slate-300";

    if (evaluation) {
        if (evaluation.evaluation.toLowerCase().includes("excelente") || evaluation.evaluation.toLowerCase().includes("ótima") || evaluation.similarityAssessment.toLowerCase().includes("muito similar")) {
            feedbackIcon = <CheckCircleIcon className="w-5 h-5 mr-2 text-green-400" />;
            evaluationColor = "text-green-300";
        } else if (evaluation.evaluation.toLowerCase().includes("precisa") || evaluation.similarityAssessment.toLowerCase().includes("diferente")) {
            feedbackIcon = <XCircleIcon className="w-5 h-5 mr-2 text-red-400" />;
            evaluationColor = "text-red-300";
        } else { 
            feedbackIcon = <SparklesIcon className="w-5 h-5 mr-2 text-sky-400" />;
            evaluationColor = "text-sky-300";
        }
    }

    return (
      <div className="mt-3 p-4 bg-slate-700/60 rounded-lg text-sm space-y-2">
        <p className="text-slate-400">
            Você praticou: <strong className="text-sky-300 font-medium">"{wordEntry.practiced_text || "N/A"}"</strong>
        </p>
        <p className="text-slate-300">
            Sua pronúncia (transcrito): <strong className="text-sky-400 font-medium">{isLoadingTranscription ? "Transcrevendo..." : (wordEntry.transcription || "N/A")}</strong>
        </p>
        {isLoadingEvaluation && <div className="flex items-center text-slate-400"><LoadingSpinner size="w-4 h-4" color="text-sky-400" /><span className="ml-2">Avaliando pronúncia...</span></div>}
        {evaluation && !isLoadingEvaluation && (
          <>
            <div className={`flex items-center ${evaluationColor}`}>
              {feedbackIcon}
              <p><strong className="font-semibold">Avaliação:</strong> {evaluation.evaluation}</p>
            </div>
            <p className="text-slate-300"><strong className="font-semibold text-slate-100">Conselho:</strong> {evaluation.advice}</p>
            <p className="text-slate-400 text-xs"><strong className="font-semibold text-slate-200">Similaridade:</strong> {evaluation.similarityAssessment}</p>
          </>
        )}
         {!evaluation && !isLoadingEvaluation && wordEntry.transcription && wordEntry.transcription !== "Falha na transcrição" && wordEntry.transcription !== "Erro na transcrição" && (
            <p className="text-slate-400">Aguardando avaliação da pronúncia...</p>
        )}
      </div>
    );
  };


  return (
    <li className="bg-slate-800 p-6 rounded-xl shadow-lg transition-all duration-300 hover:shadow-sky-500/30 mb-4">
      <div className="flex justify-between items-start mb-3">
        <h3 className="text-2xl font-bold text-sky-400 break-all">{wordEntry.text}</h3>
        <button
          onClick={() => onRemoveWord(wordEntry.id)}
          className="text-slate-500 hover:text-red-500 transition-colors p-1 rounded-full"
          aria-label="Remover palavra"
        >
          <TrashIcon />
        </button>
      </div>
      <p className="text-xs text-slate-500 mb-4">Adicionada em: {new Date(wordEntry.added_date).toLocaleDateString('pt-BR')}</p>

      {error && <p className="text-red-400 text-sm mb-3 bg-red-900/30 p-2 rounded-md">{error}</p>}

      <div className="mb-4">
        {(!wordEntry.ai_insights || isLoadingInsights) && (
          <button
            onClick={handleFetchInsights}
            disabled={isLoadingInsights}
            className="w-full bg-sky-600 hover:bg-sky-700 text-white font-medium py-2 px-4 rounded-lg flex items-center justify-center gap-2 transition-colors duration-300 disabled:opacity-60 disabled:cursor-not-allowed"
            aria-label="Obter dicas da Inteligência Artificial para esta palavra"
          >
            {isLoadingInsights ? <LoadingSpinner size="w-5 h-5" /> : <SparklesIcon />}
            {wordEntry.ai_insights ? "Atualizar Dicas da IA" : "Obter Dicas da IA"}
          </button>
        )}
        {isLoadingInsights && !wordEntry.ai_insights && <div className="flex justify-center py-2"><LoadingSpinner /></div>}
        
        {wordEntry.ai_insights && !isLoadingInsights && (
          <div className="space-y-3 mt-3 p-4 bg-slate-700/50 rounded-lg">
            <button
              onClick={handleFetchInsights}
              disabled={isLoadingInsights}
              className="w-full mb-3 bg-sky-700 hover:bg-sky-800 text-white text-xs font-medium py-1.5 px-3 rounded-md flex items-center justify-center gap-1.5 transition-colors duration-300 disabled:opacity-60 disabled:cursor-not-allowed"
              aria-label="Atualizar dicas da Inteligência Artificial para esta palavra"
            >
              <SparklesIcon className="w-4 h-4" />
              Atualizar Dicas (Idioma: {selectedLanguage})
            </button>
            {wordEntry.ai_insights.sentences.length > 0 && (
              <div>
                <h4 className="font-semibold text-sky-300 mb-2">Frases de Exemplo:</h4>
                <ul className="space-y-4 text-sm">
                  {wordEntry.ai_insights.sentences.map((s, i) => (
                    <li key={i} className="p-3 bg-slate-600/30 rounded-md">
                      <p className="text-slate-200 mb-1"><strong className="font-medium text-slate-100">Original (EN):</strong> {s.original}</p>
                      <p className="text-slate-400 mb-1 text-xs"><strong className="font-medium text-slate-300">Tradução ({selectedLanguage}):</strong> {s.translation}</p>
                      <p className="text-sky-300 italic text-xs"><strong className="font-medium text-sky-200 not-italic">Forma curta (EN):</strong> {s.shortened}</p>
                    </li>
                  ))}
                </ul>
              </div>
            )}
            {wordEntry.ai_insights.tips.length > 0 && (
              <div className="pt-3 mt-3 border-t border-slate-600/50">
                <h4 className="font-semibold text-sky-300 mb-1">Dicas de Uso:</h4>
                <ul className="space-y-2 text-sm">
                  {wordEntry.ai_insights.tips.map((t, i) => (
                    <li key={i} className="p-3 bg-slate-600/30 rounded-md">
                        <p className="text-slate-200 mb-1"><strong className="font-medium text-slate-100">Dica (EN):</strong> {t.original}</p>
                        <p className="text-slate-400 text-xs"><strong className="font-medium text-slate-300">Tradução ({selectedLanguage}):</strong> {t.translation}</p>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="border-t border-slate-700 pt-4">
        <h4 className="font-semibold text-lg text-sky-300 mb-3">Pratique a Pronúncia</h4>
        
        <div className="mb-3 space-y-2">
            <p className="text-sm text-slate-300">O que você quer praticar?</p>
            <div className="flex flex-wrap gap-x-4 gap-y-2 items-center text-sm">
                <label className="flex items-center gap-1.5 cursor-pointer hover:text-sky-300 transition-colors">
                    <input type="radio" name={`practiceTarget-${wordEntry.id}`} value="word" checked={practiceTargetType === 'word'} onChange={handlePracticeTargetChange} className="form-radio accent-sky-500 bg-slate-600 border-slate-500 focus:ring-sky-500" />
                    Palavra: "{wordEntry.text}"
                </label>
                {wordEntry.ai_insights?.sentences?.map((sentence, index) => (
                    <label key={index} className="flex items-center gap-1.5 cursor-pointer hover:text-sky-300 transition-colors">
                        <input type="radio" name={`practiceTarget-${wordEntry.id}`} value={`sentence${index + 1}` as PracticeTargetType} checked={practiceTargetType === `sentence${index + 1}` as PracticeTargetType} onChange={handlePracticeTargetChange} className="form-radio accent-sky-500 bg-slate-600 border-slate-500 focus:ring-sky-500" />
                        Frase {index + 1}
                    </label>
                ))}
            </div>
        </div>

        <div className="flex items-center gap-3 mb-2">
          {!isRecording ? (
            <button
              onClick={startRecording}
              disabled={isLoadingTranscription || isLoadingEvaluation}
              className="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-lg flex items-center gap-2 transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              aria-label={`Gravar pronúncia de ${practiceTargetType === 'word' ? 'palavra' : 'frase'}`}
            >
              <MicrophoneIcon /> Gravar
            </button>
          ) : (
            <button
              onClick={stopRecording}
              disabled={isLoadingTranscription || isLoadingEvaluation} 
              className="bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-lg flex items-center gap-2 transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              aria-label="Parar gravação"
            >
              <StopIcon /> Parar
            </button>
          )}
          {(isLoadingTranscription || isLoadingEvaluation) && <LoadingSpinner size="w-5 h-5" />}
        </div>
        <PronunciationFeedback />
      </div>
    </li>
  );
};

export default WordItem;