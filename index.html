<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>LingoLearn - Memorizador de Vocabulário</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    body {
      font-family: 'Inter', sans-serif; /* Using a common sans-serif font from Tailwind's default config */
    }
    /* Custom scrollbar for webkit browsers */
    ::-webkit-scrollbar {
      width: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 10px;
    }
    ::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 10px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
    /* Ensure inputs in auth form are visible */
    input:-webkit-autofill,
    input:-webkit-autofill:hover,
    input:-webkit-autofill:focus,
    input:-webkit-autofill:active {
        -webkit-box-shadow: 0 0 0 30px #374151 inset !important; /* bg-slate-700 */
        -webkit-text-fill-color: #f1f5f9 !important; /* text-slate-100 */
    }
  </style>
<script type="importmap">
{
  "imports": {
    "react/": "https://esm.sh/react@^18.2.0/",
    "react": "https://esm.sh/react@^18.2.0",
    "@google/genai": "https://esm.sh/@google/genai@^1.2.0",
    "react-dom/": "https://esm.sh/react-dom@^18.2.0/",
    "@supabase/supabase-js": "https://esm.sh/@supabase/supabase-js@2.43.4"
  }
}
</script>
</head>
<body class="bg-slate-900 text-slate-100">
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html><link rel="stylesheet" href="index.css">
<script src="index.tsx" type="module"></script>
