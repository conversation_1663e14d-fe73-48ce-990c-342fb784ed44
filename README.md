# LingoLearn - Memorizador de Vocabulário Inglês (Offline-First)

Um aplicativo web moderno para memorizar vocabulário em inglês, com recursos de IA para dicas de uso e avaliação de pronúncia. **Agora com suporte offline-first!**

## ✨ Funcionalidades

- 📝 **Adicionar Palavras**: Adicione palavras ao seu vocabulário pessoal
- 🤖 **Dicas de IA**: Obtenha exemplos de uso e dicas contextuais usando Gemini AI
- 🎤 **Avaliação de Pronúncia**: Pratique a pronúncia e receba feedback da IA
- 🌐 **Suporte Multilíngue**: Interface disponível em português, espanhol, francês e inglês
- 📱 **Offline-First**: Funciona completamente offline, dados salvos localmente primeiro
- 🔄 **Sincronização Manual**: Botão para sincronizar dados com a nuvem quando necessário
- ☁️ **Backup na Nuvem**: Dados sincronizados automaticamente com Supabase quando online
- 🔐 **Autenticação Segura**: Sistema de login/registro com Supabase Auth
- 🔍 **Status de Sincronização**: Visualize o status de sincronização e dados pendentes

## 🆕 Novidades da Versão Offline-First

### Principais Melhorias
- **Funciona Offline**: O app agora funciona completamente sem conexão à internet
- **Armazenamento Local**: Todos os dados são salvos primeiro no localStorage do navegador
- **Sincronização Inteligente**: Sincronização manual com detecção de conflitos
- **Indicadores Visuais**: Status de conectividade e dados pendentes sempre visíveis
- **Backup Automático**: Sistema de backup local para prevenir perda de dados
- **Resolução de Conflitos**: Interface para resolver conflitos de sincronização

### Como Funciona
1. **Dados Locais Primeiro**: Todas as operações (adicionar, editar, remover) são feitas localmente
2. **Sincronização Manual**: Use o botão "Sincronizar" para enviar/receber dados da nuvem
3. **Detecção de Conflitos**: O sistema detecta e permite resolver conflitos de dados
4. **Fallback Offline**: Se não há conexão, o app continua funcionando normalmente

## 🛠️ Tecnologias Utilizadas

- **Frontend**: React 18 + TypeScript + Vite
- **Estilização**: Tailwind CSS
- **Backend**: Supabase (PostgreSQL + Auth + Real-time)
- **IA**: Google Gemini API
- **Armazenamento Local**: localStorage API
- **Sincronização**: Custom sync service com detecção de conflitos

## 📦 Configuração do Projeto

### Pré-requisitos

- Node.js 18+
- npm ou yarn
- Conta no Supabase
- Chave da API do Google Gemini

### Instalação

1. Clone o repositório:
```bash
git clone <url-do-repositorio>
cd lingolearn
```

2. Instale as dependências:
```bash
npm install
```

3. Configure as variáveis de ambiente:
Crie um arquivo `.env.local` na raiz do projeto:
```env
GEMINI_API_KEY=sua_chave_da_api_gemini
```

4. Configure o Supabase:
- Crie um novo projeto no [Supabase](https://supabase.com)
- Execute o script SQL em `supabase_schema.sql` no SQL Editor
- Atualize as credenciais do Supabase em `services/supabaseService.ts`

5. Execute o projeto:
```bash
npm run dev
```

## 📱 Como Usar

### Funcionalidades Básicas
1. **Registro/Login**: Crie uma conta ou faça login
2. **Adicionar Palavras**: Digite uma palavra em inglês e clique em "Adicionar"
3. **Obter Dicas**: Clique no ícone de lâmpada para obter exemplos e dicas de uso
4. **Praticar Pronúncia**: Clique no ícone de microfone para gravar sua pronúncia
5. **Avaliar Pronúncia**: A IA analisará sua pronúncia e dará feedback

### Funcionalidades Offline
1. **Uso Offline**: O app funciona normalmente mesmo sem internet
2. **Sincronização**: Clique no botão "Sincronizar" para enviar/receber dados
3. **Status Visual**: Veja indicadores de conectividade e dados pendentes
4. **Detalhes de Sync**: Clique no ícone "i" ao lado do botão para ver estatísticas

## 🔄 Sistema de Sincronização

### Estados de Sincronização
- **🟢 Sincronizado**: Dados estão em sincronia com a nuvem
- **🟡 Pendente**: Dados locais aguardando sincronização
- **🔴 Offline**: Sem conexão com a internet
- **⚠️ Conflito**: Conflitos detectados que precisam ser resolvidos

### Botão de Sincronização
- **Sincronizar**: Envia dados locais para a nuvem e baixa atualizações
- **Status Visual**: Mostra estado atual da sincronização
- **Detalhes**: Painel com estatísticas detalhadas de sincronização

## 🤖 Funcionalidades da IA

### Dicas de Uso (Gemini AI)
- Exemplos de frases com a palavra
- Traduções contextuais
- Dicas de uso em diferentes situações

### Avaliação de Pronúncia
- Análise da pronúncia gravada
- Feedback sobre clareza e precisão
- Sugestões de melhoria

## 🏗️ Arquitetura do Sistema

### Serviços Principais
- **`localStorageService.ts`**: Gerenciamento de dados locais
- **`syncService.ts`**: Lógica de sincronização com a nuvem
- **`supabaseService.ts`**: Interface com Supabase
- **`useOfflineData.ts`**: Hook React para gerenciar dados offline

### Fluxo de Dados
1. **Operações Locais**: Todas as mudanças são salvas localmente primeiro
2. **Marcação para Sync**: Dados modificados são marcados para sincronização
3. **Sincronização**: Processo manual envia/recebe dados da nuvem
4. **Resolução de Conflitos**: Sistema detecta e resolve conflitos automaticamente

## 🤝 Contribuição

1. Faça um fork do projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT.

## 🆘 Suporte

Se você encontrar algum problema ou tiver sugestões, por favor abra uma issue no GitHub.

---

**LingoLearn** - Seu assistente pessoal para dominar o vocabulário em inglês! 🚀
*Agora com suporte offline completo!* 📱
