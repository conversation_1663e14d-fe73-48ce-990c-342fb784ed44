import React, { useState, useEffect, useCallback } from 'react';
import { WordEntry, SupportedLanguage, LanguageOption, languageNameMap, SupabaseSession, SupabaseUser } from './types';
import { 
  onAuthStateChange,
  AuthSubscriptionResult, 
  signOutUser,
  fetchWordsForUser,
  addWordToSupabase,
  updateWordInSupabase,
  deleteWordFromSupabase,
  supabase 
} from './services/supabaseService';
import WordInputForm from './components/WordInputForm';
import WordItem from './components/WordItem';
import LanguageSelector from './components/LanguageSelector';
import Auth from './components/Auth'; 
import LoadingSpinner from './components/LoadingSpinner';

const App: React.FC = () => {
  const [session, setSession] = useState<SupabaseSession | null>(null);
  const [user, setUser] = useState<SupabaseUser | null>(null);
  const [words, setWords] = useState<WordEntry[]>([]);
  const [isLoadingWords, setIsLoadingWords] = useState<boolean>(true); 
  const [isLoadingAuth, setIsLoadingAuth] = useState<boolean>(true); 
  const [appError, setAppError] = useState<string | null>(null);
  
  const storedLang = localStorage.getItem('lingoLearnSelectedLanguage') as SupportedLanguage | null;
  const [selectedLanguage, setSelectedLanguage] = useState<SupportedLanguage>(storedLang || 'pt-BR');

  const languageOptions: LanguageOption[] = [
    { code: 'pt-BR', name: languageNameMap['pt-BR'] },
    { code: 'es-ES', name: languageNameMap['es-ES'] },
    { code: 'fr-FR', name: languageNameMap['fr-FR'] },
  ];

  useEffect(() => {
    setIsLoadingAuth(true); 

    let authHandler: AuthSubscriptionResult | null = null;
    let pollInterval: number | undefined;

    // First, try to get current session immediately
    supabase.auth.getSession().then(({ data: { session: activeSession } }) => {
      setSession(activeSession);
      setUser(activeSession?.user ?? null);
      setIsLoadingAuth(false); // Initial check done

      // Now, set up the listener for subsequent changes
      authHandler = onAuthStateChange((event, sessionState) => {
        setSession(sessionState);
        setUser(sessionState?.user ?? null);
        setIsLoadingAuth(false); // Keep auth loading false after initial check
        if (event === 'SIGNED_OUT' || !sessionState) {
          setWords([]); 
          setIsLoadingWords(false); 
        }
      });

      if (authHandler && authHandler.isFunctional === false) {
        console.warn("Auth listener not functional, falling back to polling.");
        const thirtySeconds = 30 * 1000;
        pollInterval = window.setInterval(async () => {
          console.log("Polling for session changes...");
          const { data: { session: currentSessionPolled } } = await supabase.auth.getSession();
          // Check if session state has actually changed compared to current state
          if ((currentSessionPolled && !session) || 
              (!currentSessionPolled && session) || 
              (currentSessionPolled && session && currentSessionPolled.access_token !== session.access_token)) {
            console.log("Session change detected via polling.");
            // Manually trigger the same logic as onAuthStateChange callback
            setSession(currentSessionPolled);
            setUser(currentSessionPolled?.user ?? null);
            if (!currentSessionPolled) {
              setWords([]);
              setIsLoadingWords(false);
            }
          }
        }, thirtySeconds);
      }
    });
    
    return () => {
      if (authHandler && typeof authHandler.unsubscribe === 'function') {
        authHandler.unsubscribe();
      }
      if (pollInterval) {
        clearInterval(pollInterval);
      }
    };
  }, []); // Run once on mount

  useEffect(() => {
    if (session && !isLoadingAuth) { 
      setIsLoadingWords(true);
      setAppError(null);
      fetchWordsForUser()
        .then(loadedWords => {
          setWords(loadedWords.sort((a, b) => new Date(b.added_date).getTime() - new Date(a.added_date).getTime()));
        })
        .catch(error => {
          console.error("Failed to load words from Supabase:", error);
          setAppError("Falha ao carregar palavras do servidor.");
          setWords([]); 
        })
        .finally(() => {
          setIsLoadingWords(false);
        });
    } else if (!session && !isLoadingAuth) { 
        setWords([]);
        setIsLoadingWords(false);
    }
  }, [session, isLoadingAuth]);


  useEffect(() => {
    localStorage.setItem('lingoLearnSelectedLanguage', selectedLanguage);
  }, [selectedLanguage]);

  const handleAddWord = useCallback(async (text: string) => {
    if (!session) {
        setAppError("Você precisa estar logado para adicionar palavras.");
        return;
    }
    const normalizedText = text.toLowerCase();
    if (words.some(word => word.text.toLowerCase() === normalizedText)) {
        setAppError(`A palavra "${text}" já foi adicionada.`);
        setTimeout(() => setAppError(null), 3000);
        return;
    }
    
    const newWordData = { text }; 
    const addedWord = await addWordToSupabase(newWordData);
    
    if (addedWord) {
      setWords(prevWords => [addedWord, ...prevWords].sort((a, b) => new Date(b.added_date).getTime() - new Date(a.added_date).getTime()));
      setAppError(null);
    } else {
      setAppError("Falha ao adicionar palavra no servidor.");
    }
  }, [words, session]);

  const handleRemoveWord = useCallback(async (id: string) => {
    if (!session) return;
    
    const originalWords = [...words];
    setWords(prevWords => prevWords.filter(word => word.id !== id)); 

    const success = await deleteWordFromSupabase(id);
    if (!success) {
      setAppError("Falha ao remover palavra do servidor.");
      setWords(originalWords); 
    } else {
      setAppError(null);
    }
  }, [session, words]);

  const handleUpdateWord = useCallback(async (updatedWordData: WordEntry) => {
    if (!session) return;

    const originalWords = [...words];
    setWords(prevWords =>
      prevWords.map(word => (word.id === updatedWordData.id ? updatedWordData : word)) 
    );

    const { id, user_id, added_date, ...updatePayload } = updatedWordData; 
    const updatedWord = await updateWordInSupabase(id, updatePayload);
    
    if (updatedWord) {
      setWords(prevWords =>
        prevWords.map(word => (word.id === updatedWord.id ? updatedWord : word))
      );
      setAppError(null);
    } else {
      setAppError("Falha ao atualizar palavra no servidor.");
      setWords(originalWords); 
    }
  }, [session, words]);

  const handleSignOut = async () => {
    setAppError(null);
    const { error } = await signOutUser();
    if (error) {
      setAppError("Erro ao sair: " + error.message);
    }
    // Auth state change handler (listener or polling) will update session, user, and words
  };

  if (isLoadingAuth) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-slate-900">
        <div className="text-center">
          <LoadingSpinner size="w-12 h-12" />
          <h1 className="text-2xl text-sky-400 font-bold mt-4">Verificando autenticação...</h1>
        </div>
      </div>
    );
  }

  if (!session) {
    return <Auth onAuthSuccess={() => { /* Auth state change now primarily handled by listener/polling */ }} />;
  }
  
  return (
    <div className="min-h-screen p-4 md:p-8 bg-gradient-to-br from-slate-900 to-slate-800">
      <header className="text-center mb-6 md:mb-10 relative">
         <div className="max-w-xs mx-auto mb-4 sm:absolute sm:top-2 sm:right-0 z-10">
           <LanguageSelector
            options={languageOptions}
            selectedLanguage={selectedLanguage}
            onLanguageChange={setSelectedLanguage}
          />
        </div>
        <div className="sm:absolute sm:top-2 sm:left-0 text-left max-w-xs mx-auto mb-2 sm:mb-0 z-0">
          {user && (
            <div className="text-xs text-slate-400 bg-slate-800/50 p-2 rounded-lg">
              <p>Logado como: <span className="font-semibold text-sky-300">{user.email}</span></p>
              <button 
                onClick={handleSignOut} 
                className="mt-1 text-red-400 hover:text-red-300 font-semibold"
              >
                Sair
              </button>
            </div>
          )}
        </div>

        <h1 className="text-4xl sm:text-5xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-sky-400 to-cyan-300 py-2">
          LingoLearn
        </h1>
        <p className="text-slate-400 text-base sm:text-lg mt-2">Seu assistente pessoal para dominar o vocabulário em inglês.</p>
      </header>

      <main className="max-w-2xl mx-auto">
        <WordInputForm onAddWord={handleAddWord} isLoading={isLoadingWords && !!session} /> 
        
        {appError && (
          <div className="mb-4 p-3 bg-red-500/20 text-red-300 border border-red-500 rounded-lg text-center">
            {appError}
            <button onClick={() => setAppError(null)} className="ml-2 text-xs underline">(Fechar)</button>
          </div>
        )}

        {isLoadingWords && session && <div className="flex justify-center py-10"><LoadingSpinner size="w-10 h-10" /> <p className="ml-3 text-slate-400 text-lg">Carregando palavras...</p></div>}

        {!isLoadingWords && words.length === 0 && session && (
          <div className="text-center text-slate-500 py-10">
            <p className="text-xl mb-2">Nenhuma palavra adicionada ainda.</p>
            <p>Comece adicionando palavras que você deseja aprender!</p>
          </div>
        )}

        {!isLoadingWords && words.length > 0 && session && (
          <ul className="space-y-6">
            {words.map(word => (
              <WordItem
                key={word.id}
                wordEntry={word}
                onRemoveWord={handleRemoveWord}
                onUpdateWord={handleUpdateWord}
                selectedLanguage={selectedLanguage}
              />
            ))}
          </ul>
        )}
      </main>
      <footer className="text-center mt-12 py-6 border-t border-slate-700">
        <p className="text-slate-500 text-sm">
          LingoLearn &copy; {new Date().getFullYear()}. Dados salvos na nuvem com Supabase.
        </p>
         <p className="text-xs text-slate-600 mt-1">
            Powered by Gemini API & Supabase.
        </p>
      </footer>
    </div>
  );
};

export default App;
