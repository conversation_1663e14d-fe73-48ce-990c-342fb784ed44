import { WordEntry } from '../types';
import { LocalWordEntry, getWordsNeedingSync, markWordAsSynced, updateLastSyncTimestamp, loadFromLocalStorage, saveToLocalStorage } from './localStorageService';
import { fetchWordsForUser, addWordToSupabase, updateWordInSupabase, deleteWordFromSupabase } from './supabaseService';

export interface SyncResult {
  success: boolean;
  syncedCount: number;
  errorCount: number;
  errors: string[];
  conflictsCount: number;
}

export interface SyncStatus {
  isOnline: boolean;
  isSyncing: boolean;
  lastSyncTime: string | null;
  pendingCount: number;
  hasConflicts: boolean;
}

// Verificar se está online
export const checkOnlineStatus = (): boolean => {
  return navigator.onLine;
};

// Sincronizar dados locais com Supabase
export const syncWithSupabase = async (): Promise<SyncResult> => {
  const result: SyncResult = {
    success: false,
    syncedCount: 0,
    errorCount: 0,
    errors: [],
    conflictsCount: 0
  };

  if (!checkOnlineStatus()) {
    result.errors.push('Sem conexão com a internet');
    return result;
  }

  try {
    // 1. Buscar dados do servidor
    const serverWords = await fetchWordsForUser();
    const localData = loadFromLocalStorage();
    const localWords = localData.words;

    // 2. Sincronizar palavras locais para o servidor
    const wordsToSync = getWordsNeedingSync();
    
    for (const localWord of wordsToSync) {
      try {
        if ((localWord as any).markedForDeletion) {
          // Deletar palavra do servidor
          if (localWord.id && !localWord.id.startsWith('temp_')) {
            const success = await deleteWordFromSupabase(localWord.id);
            if (success) {
              // Remover da lista local
              const wordIndex = localData.words.findIndex(w => w.id === localWord.id);
              if (wordIndex !== -1) {
                localData.words.splice(wordIndex, 1);
              }
              result.syncedCount++;
            } else {
              result.errorCount++;
              result.errors.push(`Erro ao deletar palavra: ${localWord.text}`);
            }
          }
        } else if (localWord.id.startsWith('temp_')) {
          // Adicionar nova palavra ao servidor
          const addedWord = await addWordToSupabase({ text: localWord.text });
          if (addedWord) {
            markWordAsSynced(localWord.localId || localWord.id, addedWord.id);
            result.syncedCount++;
          } else {
            result.errorCount++;
            result.errors.push(`Erro ao adicionar palavra: ${localWord.text}`);
          }
        } else {
          // Atualizar palavra existente
          const success = await updateWordInSupabase(localWord.id, localWord);
          if (success) {
            const wordIndex = localData.words.findIndex(w => w.id === localWord.id);
            if (wordIndex !== -1) {
              localData.words[wordIndex].syncStatus = 'synced';
              localData.words[wordIndex].needsSync = false;
            }
            result.syncedCount++;
          } else {
            result.errorCount++;
            result.errors.push(`Erro ao atualizar palavra: ${localWord.text}`);
          }
        }
      } catch (error) {
        result.errorCount++;
        result.errors.push(`Erro ao sincronizar palavra ${localWord.text}: ${error}`);
      }
    }

    // 3. Sincronizar dados do servidor para local (detectar conflitos)
    for (const serverWord of serverWords) {
      const localWord = localWords.find(w => w.id === serverWord.id);
      
      if (!localWord) {
        // Palavra existe no servidor mas não localmente - adicionar
        const newLocalWord: LocalWordEntry = {
          ...serverWord,
          syncStatus: 'synced',
          lastModified: new Date().toISOString(),
          needsSync: false
        };
        localData.words.push(newLocalWord);
      } else if (localWord.syncStatus === 'synced') {
        // Verificar se houve mudanças no servidor
        const serverModified = new Date(serverWord.added_date).getTime();
        const localModified = new Date(localWord.lastModified).getTime();
        
        if (serverModified > localModified) {
          // Servidor tem versão mais recente
          Object.assign(localWord, serverWord);
          localWord.syncStatus = 'synced';
          localWord.lastModified = new Date().toISOString();
          localWord.needsSync = false;
        }
      } else if (localWord.syncStatus === 'pending') {
        // Possível conflito - marcar para revisão manual
        localWord.syncStatus = 'conflict';
        result.conflictsCount++;
      }
    }

    // 4. Salvar dados atualizados
    saveToLocalStorage(localData);
    updateLastSyncTimestamp();

    result.success = result.errorCount === 0;
    
  } catch (error) {
    result.errors.push(`Erro geral de sincronização: ${error}`);
    result.errorCount++;
  }

  return result;
};

// Obter status atual de sincronização
export const getSyncStatus = (): SyncStatus => {
  const localData = loadFromLocalStorage();
  const pendingWords = localData.words.filter(w => w.needsSync || w.syncStatus === 'pending');
  const conflictWords = localData.words.filter(w => w.syncStatus === 'conflict');

  return {
    isOnline: checkOnlineStatus(),
    isSyncing: false, // Será gerenciado pelo componente
    lastSyncTime: localData.lastSyncTimestamp,
    pendingCount: pendingWords.length,
    hasConflicts: conflictWords.length > 0
  };
};

// Resolver conflito manualmente
export const resolveConflict = async (wordId: string, resolution: 'keep-local' | 'keep-server'): Promise<boolean> => {
  const localData = loadFromLocalStorage();
  const word = localData.words.find(w => w.id === wordId);
  
  if (!word || word.syncStatus !== 'conflict') {
    return false;
  }

  try {
    if (resolution === 'keep-local') {
      // Forçar upload da versão local
      const success = await updateWordInSupabase(word.id, word);
      if (success) {
        word.syncStatus = 'synced';
        word.needsSync = false;
        word.lastModified = new Date().toISOString();
      }
      return success;
    } else {
      // Buscar versão do servidor e sobrescrever local
      const serverWords = await fetchWordsForUser();
      const serverWord = serverWords.find(w => w.id === wordId);
      
      if (serverWord) {
        Object.assign(word, serverWord);
        word.syncStatus = 'synced';
        word.needsSync = false;
        word.lastModified = new Date().toISOString();
        saveToLocalStorage(localData);
        return true;
      }
    }
  } catch (error) {
    console.error('Erro ao resolver conflito:', error);
  }
  
  return false;
};

// Forçar sincronização completa (download do servidor)
export const forceDownloadFromServer = async (): Promise<boolean> => {
  try {
    const serverWords = await fetchWordsForUser();
    const localData = loadFromLocalStorage();
    
    // Converter palavras do servidor para formato local
    const localWords: LocalWordEntry[] = serverWords.map(word => ({
      ...word,
      syncStatus: 'synced' as const,
      lastModified: new Date().toISOString(),
      needsSync: false
    }));
    
    localData.words = localWords;
    saveToLocalStorage(localData);
    updateLastSyncTimestamp();
    
    return true;
  } catch (error) {
    console.error('Erro ao forçar download:', error);
    return false;
  }
};

// Listener para mudanças de conectividade
export const setupConnectivityListener = (callback: (isOnline: boolean) => void): (() => void) => {
  const handleOnline = () => callback(true);
  const handleOffline = () => callback(false);
  
  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);
  
  return () => {
    window.removeEventListener('online', handleOnline);
    window.removeEventListener('offline', handleOffline);
  };
};
