import React, { useState } from 'react';
import { signUpUser, signInUser } from '../services/supabaseService';
import LoadingSpinner from './LoadingSpinner';

interface AuthProps {
  onAuthSuccess: () => void; // Callback to refresh app state on successful auth
}

const Auth: React.FC<AuthProps> = ({ onAuthSuccess }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLogin, setIsLogin] = useState(true); // Toggle between Login and Sign Up
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setMessage(null);

    if (isLogin) {
      const { error: signInError } = await signInUser(email, password);
      if (signInError) {
        setError(signInError.message || 'Falha ao entrar. Verifique suas credenciais.');
      } else {
        setMessage('Login bem-sucedido! Redirecionando...');
        onAuthSuccess(); // App.tsx will handle session update via onAuthStateChange
      }
    } else {
      const { error: signUpError, user } = await signUpUser(email, password);
      if (signUpError) {
        setError(signUpError.message || 'Falha ao registrar. Tente novamente.');
      } else if (user) {
         if (user.identities && user.identities.length === 0) {
            // This case might indicate an issue where Supabase considers the email taken
            // but doesn't return a specific error code for it in older versions,
            // or if email confirmation is pending but the user object is still returned.
            setError("Este e-mail já pode estar registrado ou requer confirmação. Tente fazer login ou verifique seu e-mail.");
        } else {
            setMessage('Registro bem-sucedido! Um e-mail de confirmação foi enviado (se habilitado). Por favor, faça login.');
            setIsLogin(true); // Switch to login form
        }
      } else {
         setError('Falha ao registrar. Tente novamente.');
      }
    }
    setLoading(false);
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-slate-900 p-4">
      <div className="w-full max-w-md p-8 space-y-6 bg-slate-800 rounded-xl shadow-2xl">
        <div className="text-center">
           <h1 className="text-3xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-sky-400 to-cyan-300 py-2">
            LingoLearn
          </h1>
          <p className="text-slate-400">{isLogin ? 'Acesse sua conta' : 'Crie uma nova conta'}</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-slate-300">
              Email
            </label>
            <input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mt-1 block w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md shadow-sm placeholder-slate-400 focus:outline-none focus:ring-sky-500 focus:border-sky-500 sm:text-sm text-slate-100"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-slate-300">
              Senha
            </label>
            <input
              id="password"
              name="password"
              type="password"
              autoComplete={isLogin ? "current-password" : "new-password"}
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="mt-1 block w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md shadow-sm placeholder-slate-400 focus:outline-none focus:ring-sky-500 focus:border-sky-500 sm:text-sm text-slate-100"
              placeholder="********"
            />
          </div>
          {error && <p className="text-sm text-red-400 bg-red-900/30 p-2 rounded-md text-center">{error}</p>}
          {message && <p className="text-sm text-green-400 bg-green-900/30 p-2 rounded-md text-center">{message}</p>}
          <div>
            <button
              type="submit"
              disabled={loading}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-sky-600 hover:bg-sky-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 focus:ring-offset-slate-800 disabled:opacity-50"
            >
              {loading ? <LoadingSpinner size="w-5 h-5" /> : (isLogin ? 'Entrar' : 'Registrar')}
            </button>
          </div>
        </form>

        <p className="text-sm text-center text-slate-400">
          {isLogin ? 'Não tem uma conta?' : 'Já tem uma conta?'}
          <button
            onClick={() => { setIsLogin(!isLogin); setError(null); setMessage(null);}}
            className="ml-1 font-medium text-sky-400 hover:text-sky-300"
          >
            {isLogin ? 'Registre-se' : 'Faça login'}
          </button>
        </p>
      </div>
       <footer className="text-center mt-8 py-6">
        <p className="text-slate-500 text-sm">
          LingoLearn &copy; {new Date().getFullYear()}.
        </p>
         <p className="text-xs text-slate-600 mt-1">
            Powered by Gemini API & Supabase.
        </p>
      </footer>
    </div>
  );
};

export default Auth;