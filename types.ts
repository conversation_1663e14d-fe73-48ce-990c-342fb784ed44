export interface SentenceExample {
  original: string;
  translation: string;
  shortened: string;
}

export interface AITip {
  original: string;
  translation: string;
}

export interface AIInsights {
  sentences: SentenceExample[];
  tips: AITip[];
}

export interface PronunciationEvaluation {
  evaluation: string;
  advice: string;
  similarityAssessment: string;
  confidence?: number;
}

// Matches the structure in Supabase `user_words` table
export interface WordEntry {
  id: string; // Comes from Supabase (uuid)
  user_id?: string; // Comes from Supabase (uuid of the authenticated user)
  text: string;
  added_date: string; // Mapped from Supabase `created_at` or `added_date` column
  ai_insights?: AIInsights | null;
  practiced_text?: string | null;
  transcription?: string | null;
  pronunciation_evaluation?: PronunciationEvaluation | null;
}

export type SupportedLanguage = 'pt-BR' | 'es-ES' | 'fr-FR' | 'en-US';

export interface LanguageOption {
  code: SupportedLanguage;
  name: string;
}

export const languageNameMap: Record<SupportedLanguage, string> = {
  'pt-BR': 'Português (Brasil)',
  'es-ES': 'Español (España)',
  'fr-FR': 'Français (France)',
  'en-US': 'English (US)',
};

// Simplified Supabase types (you can import directly if full types are needed)
export interface SupabaseUser {
  id: string;
  email?: string;
  // Add other user properties you might need
}

export interface SupabaseSession {
  access_token: string;
  refresh_token?: string;
  user: SupabaseUser;
  // Add other session properties
}

// Tipos para sincronização offline
export interface SyncMetadata {
  syncStatus: 'synced' | 'pending' | 'conflict' | 'local-only';
  lastModified: string;
  needsSync?: boolean;
  localId?: string;
}

export interface OfflineCapableWordEntry extends WordEntry, SyncMetadata {
  // Combina WordEntry com metadados de sincronização
}