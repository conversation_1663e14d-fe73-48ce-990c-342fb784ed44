import React, { useState } from 'react';
import { SyncStatus, SyncResult } from '../services/syncService';

interface SyncButtonProps {
  syncStatus: SyncStatus;
  onSync: () => Promise<SyncResult>;
  syncStats: {
    total: number;
    synced: number;
    pending: number;
    localOnly: number;
    conflicts: number;
  };
  lastSyncResult: SyncResult | null;
}

const SyncButton: React.FC<SyncButtonProps> = ({ 
  syncStatus, 
  onSync, 
  syncStats, 
  lastSyncResult 
}) => {
  const [showDetails, setShowDetails] = useState(false);

  const handleSync = async () => {
    if (syncStatus.isSyncing) return;
    await onSync();
  };

  const formatLastSyncTime = (timestamp: string | null) => {
    if (!timestamp) return 'Nunca';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Agora mesmo';
    if (diffMins < 60) return `${diffMins} min atrás`;
    if (diffHours < 24) return `${diffHours}h atrás`;
    if (diffDays < 7) return `${diffDays}d atrás`;
    
    return date.toLocaleDateString('pt-BR');
  };

  const getSyncStatusColor = () => {
    if (!syncStatus.isOnline) return 'text-red-400';
    if (syncStatus.isSyncing) return 'text-blue-400';
    if (syncStatus.hasConflicts) return 'text-orange-400';
    if (syncStatus.pendingCount > 0) return 'text-yellow-400';
    return 'text-green-400';
  };

  const getSyncStatusText = () => {
    if (!syncStatus.isOnline) return 'Offline';
    if (syncStatus.isSyncing) return 'Sincronizando...';
    if (syncStatus.hasConflicts) return 'Conflitos detectados';
    if (syncStatus.pendingCount > 0) return `${syncStatus.pendingCount} pendente(s)`;
    return 'Sincronizado';
  };

  const getSyncIcon = () => {
    if (syncStatus.isSyncing) {
      return (
        <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      );
    }

    if (!syncStatus.isOnline) {
      return (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-12.728 12.728m0 0L12 12m-6.364 6.364L12 12m6.364-6.364L12 12" />
        </svg>
      );
    }

    if (syncStatus.hasConflicts) {
      return (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      );
    }

    return (
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
      </svg>
    );
  };

  return (
    <div className="relative">
      <div className="flex items-center space-x-2">
        {/* Botão principal de sincronização */}
        <button
          onClick={handleSync}
          disabled={syncStatus.isSyncing || !syncStatus.isOnline}
          className={`
            flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium
            transition-all duration-200 min-w-[120px] justify-center
            ${syncStatus.isOnline && !syncStatus.isSyncing
              ? 'bg-blue-600 hover:bg-blue-700 text-white'
              : 'bg-gray-600 text-gray-300 cursor-not-allowed'
            }
          `}
        >
          {getSyncIcon()}
          <span>
            {syncStatus.isSyncing ? 'Sincronizando' : 'Sincronizar'}
          </span>
        </button>

        {/* Botão de detalhes */}
        <button
          onClick={() => setShowDetails(!showDetails)}
          className="p-2 rounded-lg bg-slate-700 hover:bg-slate-600 text-slate-300 transition-colors"
          title="Ver detalhes de sincronização"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </button>
      </div>

      {/* Status de sincronização */}
      <div className={`text-xs mt-1 ${getSyncStatusColor()}`}>
        {getSyncStatusText()}
      </div>

      {/* Painel de detalhes */}
      {showDetails && (
        <div className="absolute top-full left-0 mt-2 w-80 bg-slate-800 border border-slate-600 rounded-lg p-4 shadow-lg z-50">
          <h3 className="text-sm font-semibold text-white mb-3">Status de Sincronização</h3>
          
          {/* Estatísticas */}
          <div className="space-y-2 mb-4">
            <div className="flex justify-between text-xs">
              <span className="text-slate-400">Total de palavras:</span>
              <span className="text-white">{syncStats.total}</span>
            </div>
            <div className="flex justify-between text-xs">
              <span className="text-slate-400">Sincronizadas:</span>
              <span className="text-green-400">{syncStats.synced}</span>
            </div>
            {syncStats.pending > 0 && (
              <div className="flex justify-between text-xs">
                <span className="text-slate-400">Pendentes:</span>
                <span className="text-yellow-400">{syncStats.pending}</span>
              </div>
            )}
            {syncStats.conflicts > 0 && (
              <div className="flex justify-between text-xs">
                <span className="text-slate-400">Conflitos:</span>
                <span className="text-orange-400">{syncStats.conflicts}</span>
              </div>
            )}
          </div>

          {/* Última sincronização */}
          <div className="border-t border-slate-600 pt-3">
            <div className="flex justify-between text-xs mb-2">
              <span className="text-slate-400">Última sincronização:</span>
              <span className="text-white">{formatLastSyncTime(syncStatus.lastSyncTime)}</span>
            </div>
            
            <div className="flex justify-between text-xs">
              <span className="text-slate-400">Status da conexão:</span>
              <span className={syncStatus.isOnline ? 'text-green-400' : 'text-red-400'}>
                {syncStatus.isOnline ? 'Online' : 'Offline'}
              </span>
            </div>
          </div>

          {/* Resultado da última sincronização */}
          {lastSyncResult && (
            <div className="border-t border-slate-600 pt-3 mt-3">
              <h4 className="text-xs font-medium text-white mb-2">Última Sincronização:</h4>
              <div className="space-y-1">
                <div className="flex justify-between text-xs">
                  <span className="text-slate-400">Sincronizadas:</span>
                  <span className="text-green-400">{lastSyncResult.syncedCount}</span>
                </div>
                {lastSyncResult.errorCount > 0 && (
                  <div className="flex justify-between text-xs">
                    <span className="text-slate-400">Erros:</span>
                    <span className="text-red-400">{lastSyncResult.errorCount}</span>
                  </div>
                )}
                {lastSyncResult.conflictsCount > 0 && (
                  <div className="flex justify-between text-xs">
                    <span className="text-slate-400">Conflitos:</span>
                    <span className="text-orange-400">{lastSyncResult.conflictsCount}</span>
                  </div>
                )}
              </div>
              
              {lastSyncResult.errors.length > 0 && (
                <div className="mt-2">
                  <div className="text-xs text-red-400 mb-1">Erros:</div>
                  <div className="text-xs text-slate-300 bg-slate-900 p-2 rounded max-h-20 overflow-y-auto">
                    {lastSyncResult.errors.map((error, index) => (
                      <div key={index}>• {error}</div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Botão para fechar */}
          <button
            onClick={() => setShowDetails(false)}
            className="w-full mt-3 px-3 py-1 bg-slate-700 hover:bg-slate-600 text-slate-300 text-xs rounded transition-colors"
          >
            Fechar
          </button>
        </div>
      )}
    </div>
  );
};

export default SyncButton;
